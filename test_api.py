"""API测试脚本"""

import asyncio
import httpx
import json


async def test_blocking_mode():
    """测试阻塞模式"""
    print("=== 测试阻塞模式 ===")

    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8001/extract-conflict-data",
            json={
                "query": "请分析这个矛盾纠纷案例：张三和李四因为房屋租赁问题发生争执",
                "thinking": False,
                "mode": "blocking"
            }
        )

        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")


async def test_streaming_mode():
    """测试流式模式"""
    print("\n=== 测试流式模式 ===")

    async with httpx.AsyncClient() as client:
        async with client.stream(
            "POST",
            "http://localhost:8001/extract-conflict-data",
            json={
                "query": "请分析这个矛盾纠纷案例：王五和赵六因为合同纠纷产生争议",
                "thinking": True,
                "mode": "streaming"
            }
        ) as response:
            print(f"状态码: {response.status_code}")
            async for chunk in response.aiter_text():
                if chunk:
                    print(f"流式数据: {chunk}")


async def test_health():
    """测试健康检查"""
    print("\n=== 测试健康检查 ===")

    async with httpx.AsyncClient() as client:
        response = await client.get("http://localhost:8001/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")


async def main():
    """主测试函数"""
    try:
        await test_health()
        await test_blocking_mode()
        await test_streaming_mode()
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
