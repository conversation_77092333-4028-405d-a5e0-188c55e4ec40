"""FastAPI主应用"""

import logging
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware

from models import ConflictExtractionRequest, ConflictExtractionResponse
from services import ConflictExtractionService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="矛盾纠纷结构化数据提取服务",
    description="基于FastAPI的矛盾纠纷结构化数据提取服务接口",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化服务
conflict_service = ConflictExtractionService()


@app.get("/")
async def root():
    """根路径"""
    return {"message": "矛盾纠纷结构化数据提取服务", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}


@app.post("/extract-conflict-data", response_model=ConflictExtractionResponse)
async def extract_conflict_data(request: ConflictExtractionRequest):
    """
    矛盾纠纷结构化数据提取接口

    Args:
        request: 包含query、thinking参数的请求

    Returns:
        提取的结构化数据响应
    """
    logger.info(f"收到数据提取请求: query={request.query[:100]}..., thinking={request.thinking}")

    try:
        # 统一使用streaming模式调用后台，等待完整结果后返回
        result = await conflict_service.extract_conflict_data(
            request.query,
            request.thinking
        )
        return ConflictExtractionResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理请求时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"服务内部错误: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
