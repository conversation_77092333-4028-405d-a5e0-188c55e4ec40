"""外部服务调用"""

import httpx
import json
import logging
from typing import Dict, Any, AsyncGenerator, Optional
from fastapi import HTTPException
from fastapi.responses import StreamingResponse

from models import ExternalAPIRequest
from config import EXTERNAL_API_URL, EXTERNAL_API_TOKEN, EXTERNAL_API_USER
from utils import extract_json_from_text, prepare_query

logger = logging.getLogger(__name__)


class ConflictExtractionService:
    """矛盾纠纷结构化数据提取服务"""

    def __init__(self):
        self.api_url = EXTERNAL_API_URL
        self.headers = {
            "Authorization": f"Bearer {EXTERNAL_API_TOKEN}",
            "Content-Type": "application/json"
        }

    async def extract_conflict_data_blocking(self, query: str, thinking: bool) -> Dict[str, Any]:
        """
        阻塞模式提取矛盾纠纷数据

        Args:
            query: 查询信息
            thinking: 是否开启深度思考

        Returns:
            提取的结构化数据
        """
        processed_query = prepare_query(query, thinking)

        request_data = ExternalAPIRequest(
            query=processed_query,
            response_mode="blocking",
            user=EXTERNAL_API_USER
        )

        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    self.api_url,
                    headers=self.headers,
                    json=request_data.model_dump()
                )
                response.raise_for_status()

                response_data = response.json()
                logger.info(f"外部API响应: {response_data}")

                # 从响应中提取JSON数据
                if "answer" in response_data:
                    extracted_json = extract_json_from_text(response_data["answer"])
                    if extracted_json:
                        return {
                            "success": True,
                            "data": extracted_json,
                            "message": "数据提取成功"
                        }

                return {
                    "success": False,
                    "data": None,
                    "message": "未能从响应中提取到有效的JSON数据"
                }

        except httpx.HTTPError as e:
            logger.error(f"HTTP请求错误: {e}")
            raise HTTPException(status_code=500, detail=f"外部API调用失败: {str(e)}")
        except Exception as e:
            logger.error(f"处理错误: {e}")
            raise HTTPException(status_code=500, detail=f"数据处理失败: {str(e)}")

    async def extract_conflict_data_streaming(self, query: str, thinking: bool) -> AsyncGenerator[str, None]:
        """
        流式模式提取矛盾纠纷数据

        Args:
            query: 查询信息
            thinking: 是否开启深度思考

        Yields:
            流式响应数据
        """
        processed_query = prepare_query(query, thinking)

        request_data = ExternalAPIRequest(
            query=processed_query,
            response_mode="streaming",
            user=EXTERNAL_API_USER
        )

        try:
            # 发送开始处理的消息
            yield f"data: {json.dumps({'success': False, 'data': None, 'message': '开始处理请求...'}, ensure_ascii=False)}\n\n"

            async with httpx.AsyncClient(timeout=120.0) as client:
                async with client.stream(
                    "POST",
                    self.api_url,
                    headers=self.headers,
                    json=request_data.model_dump()
                ) as response:
                    response.raise_for_status()

                    # 发送处理中的消息
                    yield f"data: {json.dumps({'success': False, 'data': None, 'message': '正在分析数据...'}, ensure_ascii=False)}\n\n"

                    accumulated_text = ""
                    final_answer = ""

                    async for line in response.aiter_lines():
                        if line:
                            line = line.strip()
                            if line.startswith("data: "):
                                try:
                                    data_json = json.loads(line[6:])  # 去掉 "data: " 前缀

                                    # 检查是否是消息事件
                                    if data_json.get("event") == "message" and "answer" in data_json:
                                        final_answer = data_json["answer"]
                                        logger.info(f"收到完整答案: {final_answer[:200]}...")

                                        # 提取JSON数据
                                        extracted_json = extract_json_from_text(final_answer)
                                        if extracted_json:
                                            yield f"data: {json.dumps({'success': True, 'data': extracted_json, 'message': '数据提取成功'}, ensure_ascii=False)}\n\n"
                                            return

                                    # 检查是否是工作流完成事件
                                    elif data_json.get("event") == "workflow_finished":
                                        if final_answer:
                                            extracted_json = extract_json_from_text(final_answer)
                                            if extracted_json:
                                                yield f"data: {json.dumps({'success': True, 'data': extracted_json, 'message': '数据提取成功'}, ensure_ascii=False)}\n\n"
                                                return

                                except json.JSONDecodeError:
                                    continue

                    # 如果没有找到有效的JSON数据
                    if final_answer:
                        extracted_json = extract_json_from_text(final_answer)
                        if extracted_json:
                            yield f"data: {json.dumps({'success': True, 'data': extracted_json, 'message': '数据提取成功'}, ensure_ascii=False)}\n\n"
                            return

                    yield f"data: {json.dumps({'success': False, 'data': None, 'message': '未能从响应中提取到有效的JSON数据'}, ensure_ascii=False)}\n\n"

        except httpx.HTTPError as e:
            logger.error(f"HTTP请求错误: {e}")
            yield f"data: {json.dumps({'success': False, 'data': None, 'message': f'外部API调用失败: {str(e)}'}, ensure_ascii=False)}\n\n"
        except Exception as e:
            logger.error(f"处理错误: {e}")
            yield f"data: {json.dumps({'success': False, 'data': None, 'message': f'数据处理失败: {str(e)}'}, ensure_ascii=False)}\n\n"
