"""外部服务调用"""

import httpx
import json
import logging
from typing import Dict, Any, AsyncGenerator, Optional
from fastapi import HTTPException
from fastapi.responses import StreamingResponse

from models import ExternalAPIRequest
from config import EXTERNAL_API_URL, EXTERNAL_API_TOKEN, EXTERNAL_API_USER
from utils import extract_json_from_text, prepare_query

logger = logging.getLogger(__name__)


class ConflictExtractionService:
    """矛盾纠纷结构化数据提取服务"""

    def __init__(self):
        self.api_url = EXTERNAL_API_URL
        self.headers = {
            "Authorization": f"Bearer {EXTERNAL_API_TOKEN}",
            "Content-Type": "application/json"
        }

    async def extract_conflict_data(self, query: str, thinking: bool) -> Dict[str, Any]:
        """
        提取矛盾纠纷数据（统一使用streaming模式，等待完整结果）

        Args:
            query: 查询信息
            thinking: 是否开启深度思考

        Returns:
            提取的结构化数据
        """
        processed_query = prepare_query(query, thinking)

        request_data = ExternalAPIRequest(
            query=processed_query,
            response_mode="streaming",
            user=EXTERNAL_API_USER
        )

        try:
            async with httpx.AsyncClient(timeout=120.0) as client:
                async with client.stream(
                    "POST",
                    self.api_url,
                    headers=self.headers,
                    json=request_data.model_dump()
                ) as response:
                    response.raise_for_status()

                    final_answer = ""
                    workflow_finished = False

                    # 收集所有流式数据，等待完整响应
                    async for line in response.aiter_lines():
                        if line:
                            line = line.strip()
                            if line.startswith("data: "):
                                try:
                                    data_json = json.loads(line[6:])  # 去掉 "data: " 前缀

                                    # 检查不同类型的事件
                                    event_type = data_json.get("event", "")

                                    if event_type == "message" and "answer" in data_json:
                                        # 保留最新的答案内容（流式响应中最后的答案是最完整的）
                                        final_answer = data_json["answer"]
                                        logger.debug(f"更新答案，当前长度: {len(final_answer)}")

                                    elif event_type == "workflow_finished":
                                        # 工作流完成，可以开始处理最终答案
                                        workflow_finished = True
                                        logger.info("工作流完成，开始处理最终答案")
                                        break

                                    elif event_type == "message_end":
                                        # 消息结束事件
                                        logger.info("消息结束事件")
                                        break

                                except json.JSONDecodeError as e:
                                    logger.debug(f"JSON解析失败: {e}, 行内容: {line[:100]}")
                                    continue

                    # 处理最终答案
                    if final_answer:
                        logger.info(f"开始从最终答案中提取JSON，答案长度: {len(final_answer)}")
                        logger.debug(f"最终答案内容: {final_answer[:500]}...")

                        extracted_json = extract_json_from_text(final_answer)
                        if extracted_json:
                            logger.info(f"成功提取JSON数据: {extracted_json}")
                            return {
                                "success": True,
                                "data": extracted_json,
                                "message": "数据提取成功"
                            }
                        else:
                            logger.warning(f"无法从最终答案中提取JSON数据")

                    return {
                        "success": False,
                        "data": None,
                        "message": "未能从响应中提取到有效的JSON数据"
                    }

        except httpx.HTTPError as e:
            logger.error(f"HTTP请求错误: {e}")
            raise HTTPException(status_code=500, detail=f"外部API调用失败: {str(e)}")
        except Exception as e:
            logger.error(f"处理错误: {e}")
            raise HTTPException(status_code=500, detail=f"数据处理失败: {str(e)}")


