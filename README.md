# 矛盾纠纷结构化数据提取服务

基于FastAPI的矛盾纠纷结构化数据提取服务接口。

## 功能特性

- 支持矛盾纠纷数据的结构化提取
- 后台统一使用streaming模式，等待完整结果后返回
- 支持深度思考模式
- 自动从响应中提取JSON结构化数据

## 安装依赖

```bash
pip install -r requirements.txt
```

## 启动服务

```bash
python start.py
```

服务将在 `http://localhost:8001` 启动。

## API接口

### POST /extract-conflict-data

矛盾纠纷结构化数据提取接口。

#### 请求参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| query | string | 是 | - | 查询信息 |
| thinking | boolean | 否 | false | 开启深度思考 |

#### 请求示例

**基本请求：**
```bash
curl -X POST "http://localhost:8001/extract-conflict-data" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "张三和李四因为房屋租赁问题发生争执",
    "thinking": false
  }'
```

**开启深度思考：**
```bash
curl -X POST "http://localhost:8001/extract-conflict-data" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "王五和赵六因为合同纠纷产生争议",
    "thinking": true
  }'
```

**使用默认参数：**
```bash
curl -X POST "http://localhost:8001/extract-conflict-data" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "小明和小红因为借款问题发生纠纷"
  }'
```

#### 响应格式

**标准响应：**
```json
{
  "success": true,
  "data": {
    "lxmc": "民间纠纷",
    "lxdm": "01",
    "dsr": [
      {
        "xm": "张三",
        "sfzh": "",
        "dhhm": ""
      },
      {
        "xm": "李四",
        "sfzh": "",
        "dhhm": ""
      }
    ]
  },
  "message": "数据提取成功"
}
```

## 其他接口

### GET /health

健康检查接口。

### GET /

根路径，返回服务信息。

## 测试

运行测试脚本：

```bash
# 测试新的API接口
python test/test_new_api.py

# 或运行其他测试
python test/simple_test.py
```

## 配置

在 `config.py` 中可以修改以下配置：

- `EXTERNAL_API_URL`: 外部API地址
- `EXTERNAL_API_TOKEN`: 外部API认证令牌
- `EXTERNAL_API_USER`: 外部API用户名

## 项目结构

```
.
├── main.py           # FastAPI主应用
├── models.py         # 数据模型定义
├── services.py       # 外部服务调用
├── utils.py          # 工具函数
├── config.py         # 配置文件
├── start.py          # 启动脚本
├── requirements.txt  # 依赖列表
├── README.md         # 说明文档
└── test/             # 测试文件夹
    ├── test_new_api.py
    ├── simple_test.py
    └── ...
```
